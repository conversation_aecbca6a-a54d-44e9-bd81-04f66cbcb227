#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
wxauto 可视化界面
基于 PySide6 的图形用户界面
"""

import sys
import os
import time
import threading
from datetime import datetime
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QTextEdit, QLineEdit, QPushButton, QLabel,
    QComboBox, QListWidget, QSplitter, QGroupBox, QCheckBox,
    QFileDialog, QMessageBox, QProgressBar, QStatusBar,
    QTableWidget, QTableWidgetItem, QHeaderView
)
from PySide6.QtCore import Qt, QThread, Signal, QTimer
from PySide6.QtGui import QFont, QIcon, QPixmap

try:
    from wxauto import WeChat
    from wxauto.msgs import FriendMessage
    WXAUTO_AVAILABLE = True
except ImportError:
    WXAUTO_AVAILABLE = False


class MessageListenerThread(QThread):
    """消息监听线程"""
    new_message = Signal(str, str, str, str)  # sender, content, msg_type, chat_name
    
    def __init__(self, wx_instance, chat_name, parent=None):
        super().__init__(parent)
        self.wx_instance = wx_instance
        self.chat_name = chat_name
        self.running = False
        
    def run(self):
        """运行监听"""
        self.running = True
        
        def message_handler(msg, chat):
            if self.running:
                self.new_message.emit(
                    msg.sender or "未知",
                    msg.content or "",
                    msg.type or "text",
                    self.chat_name
                )
        
        try:
            self.wx_instance.AddListenChat(
                nickname=self.chat_name,
                callback=message_handler
            )
            
            while self.running:
                self.msleep(100)  # 100ms检查一次
                
        except Exception as e:
            print(f"监听线程异常: {e}")
    
    def stop(self):
        """停止监听"""
        self.running = False
        try:
            self.wx_instance.RemoveListenChat(nickname=self.chat_name)
        except:
            pass
        self.quit()
        self.wait()


class WxAutoGUI(QMainWindow):
    """wxauto 主界面"""
    
    def __init__(self):
        super().__init__()
        self.wx_instance = None
        self.listener_threads = {}
        self.init_ui()
        self.init_wxauto()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("wxauto 可视化工具 v1.0")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("准备就绪")
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 创建各个选项卡
        self.create_message_tab()
        self.create_file_tab()
        self.create_listen_tab()
        self.create_contact_tab()
        self.create_log_tab()
        
    def create_message_tab(self):
        """创建消息发送选项卡"""
        message_widget = QWidget()
        layout = QVBoxLayout(message_widget)
        
        # 聊天对象选择
        target_group = QGroupBox("聊天对象")
        target_layout = QHBoxLayout(target_group)
        
        self.target_combo = QComboBox()
        self.target_combo.setEditable(True)
        self.refresh_btn = QPushButton("刷新会话")
        self.refresh_btn.clicked.connect(self.refresh_sessions)
        
        target_layout.addWidget(QLabel("选择聊天对象:"))
        target_layout.addWidget(self.target_combo)
        target_layout.addWidget(self.refresh_btn)
        
        layout.addWidget(target_group)
        
        # 消息输入区域
        message_group = QGroupBox("消息内容")
        message_layout = QVBoxLayout(message_group)
        
        self.message_text = QTextEdit()
        self.message_text.setMaximumHeight(150)
        self.message_text.setPlaceholderText("请输入要发送的消息...")
        
        # @功能
        at_layout = QHBoxLayout()
        self.at_checkbox = QCheckBox("@某人")
        self.at_input = QLineEdit()
        self.at_input.setPlaceholderText("输入要@的用户名")
        self.at_input.setEnabled(False)
        self.at_checkbox.toggled.connect(self.at_input.setEnabled)
        
        at_layout.addWidget(self.at_checkbox)
        at_layout.addWidget(self.at_input)
        at_layout.addStretch()
        
        # 发送按钮
        send_layout = QHBoxLayout()
        self.send_btn = QPushButton("发送消息")
        self.send_btn.clicked.connect(self.send_message)
        self.send_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        
        send_layout.addStretch()
        send_layout.addWidget(self.send_btn)
        
        message_layout.addWidget(self.message_text)
        message_layout.addLayout(at_layout)
        message_layout.addLayout(send_layout)
        
        layout.addWidget(message_group)
        
        # 消息历史
        history_group = QGroupBox("消息历史")
        history_layout = QVBoxLayout(history_group)
        
        history_btn_layout = QHBoxLayout()
        self.get_messages_btn = QPushButton("获取消息")
        self.get_messages_btn.clicked.connect(self.get_messages)
        self.load_more_btn = QPushButton("加载更多")
        self.load_more_btn.clicked.connect(self.load_more_messages)
        
        history_btn_layout.addWidget(self.get_messages_btn)
        history_btn_layout.addWidget(self.load_more_btn)
        history_btn_layout.addStretch()
        
        self.message_history = QTextEdit()
        self.message_history.setReadOnly(True)
        
        history_layout.addLayout(history_btn_layout)
        history_layout.addWidget(self.message_history)
        
        layout.addWidget(history_group)
        
        self.tab_widget.addTab(message_widget, "💬 消息发送")
        
    def create_file_tab(self):
        """创建文件发送选项卡"""
        file_widget = QWidget()
        layout = QVBoxLayout(file_widget)
        
        # 文件选择
        file_group = QGroupBox("文件选择")
        file_layout = QVBoxLayout(file_group)
        
        select_layout = QHBoxLayout()
        self.file_path_input = QLineEdit()
        self.file_path_input.setPlaceholderText("选择要发送的文件...")
        self.select_file_btn = QPushButton("选择文件")
        self.select_file_btn.clicked.connect(self.select_file)
        
        select_layout.addWidget(self.file_path_input)
        select_layout.addWidget(self.select_file_btn)
        
        # 发送文件按钮
        send_file_layout = QHBoxLayout()
        self.send_file_btn = QPushButton("发送文件")
        self.send_file_btn.clicked.connect(self.send_file)
        self.send_file_btn.setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; }")
        
        send_file_layout.addStretch()
        send_file_layout.addWidget(self.send_file_btn)
        
        file_layout.addLayout(select_layout)
        file_layout.addLayout(send_file_layout)
        
        layout.addWidget(file_group)
        
        # 文件发送记录
        record_group = QGroupBox("发送记录")
        record_layout = QVBoxLayout(record_group)
        
        self.file_record = QTextEdit()
        self.file_record.setReadOnly(True)
        
        record_layout.addWidget(self.file_record)
        layout.addWidget(record_group)
        
        self.tab_widget.addTab(file_widget, "📁 文件发送")
        
    def create_listen_tab(self):
        """创建消息监听选项卡"""
        listen_widget = QWidget()
        layout = QVBoxLayout(listen_widget)
        
        # 监听控制
        control_group = QGroupBox("监听控制")
        control_layout = QVBoxLayout(control_group)
        
        # 监听对象选择
        target_layout = QHBoxLayout()
        self.listen_target_combo = QComboBox()
        self.listen_target_combo.setEditable(True)
        self.start_listen_btn = QPushButton("开始监听")
        self.stop_listen_btn = QPushButton("停止监听")
        
        self.start_listen_btn.clicked.connect(self.start_listening)
        self.stop_listen_btn.clicked.connect(self.stop_listening)
        self.start_listen_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; }")
        self.stop_listen_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; }")
        
        target_layout.addWidget(QLabel("监听对象:"))
        target_layout.addWidget(self.listen_target_combo)
        target_layout.addWidget(self.start_listen_btn)
        target_layout.addWidget(self.stop_listen_btn)
        
        # 自动回复设置
        reply_layout = QHBoxLayout()
        self.auto_reply_checkbox = QCheckBox("启用自动回复")
        self.reply_text = QLineEdit()
        self.reply_text.setPlaceholderText("自动回复内容...")
        self.reply_text.setEnabled(False)
        self.auto_reply_checkbox.toggled.connect(self.reply_text.setEnabled)
        
        reply_layout.addWidget(self.auto_reply_checkbox)
        reply_layout.addWidget(self.reply_text)
        
        control_layout.addLayout(target_layout)
        control_layout.addLayout(reply_layout)
        
        layout.addWidget(control_group)
        
        # 监听消息显示
        message_group = QGroupBox("监听到的消息")
        message_layout = QVBoxLayout(message_group)
        
        self.listen_messages = QTextEdit()
        self.listen_messages.setReadOnly(True)
        
        message_layout.addWidget(self.listen_messages)
        layout.addWidget(message_group)
        
        self.tab_widget.addTab(listen_widget, "👂 消息监听")
        
    def create_contact_tab(self):
        """创建联系人管理选项卡"""
        contact_widget = QWidget()
        layout = QVBoxLayout(contact_widget)
        
        # 会话列表
        session_group = QGroupBox("会话列表")
        session_layout = QVBoxLayout(session_group)
        
        refresh_layout = QHBoxLayout()
        self.refresh_session_btn = QPushButton("刷新会话")
        self.refresh_session_btn.clicked.connect(self.refresh_sessions)
        refresh_layout.addWidget(self.refresh_session_btn)
        refresh_layout.addStretch()
        
        self.session_list = QListWidget()
        
        session_layout.addLayout(refresh_layout)
        session_layout.addWidget(self.session_list)
        
        layout.addWidget(session_group)
        
        # 群成员列表
        member_group = QGroupBox("群成员列表")
        member_layout = QVBoxLayout(member_group)
        
        get_member_layout = QHBoxLayout()
        self.get_members_btn = QPushButton("获取群成员")
        self.get_members_btn.clicked.connect(self.get_group_members)
        get_member_layout.addWidget(self.get_members_btn)
        get_member_layout.addStretch()
        
        self.member_list = QListWidget()
        
        member_layout.addLayout(get_member_layout)
        member_layout.addWidget(self.member_list)
        
        layout.addWidget(member_group)
        
        self.tab_widget.addTab(contact_widget, "👥 联系人")
        
    def create_log_tab(self):
        """创建日志选项卡"""
        log_widget = QWidget()
        layout = QVBoxLayout(log_widget)
        
        # 日志控制
        control_layout = QHBoxLayout()
        self.clear_log_btn = QPushButton("清空日志")
        self.clear_log_btn.clicked.connect(self.clear_log)
        self.save_log_btn = QPushButton("保存日志")
        self.save_log_btn.clicked.connect(self.save_log)
        
        control_layout.addWidget(self.clear_log_btn)
        control_layout.addWidget(self.save_log_btn)
        control_layout.addStretch()
        
        # 日志显示
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        
        layout.addLayout(control_layout)
        layout.addWidget(self.log_text)
        
        self.tab_widget.addTab(log_widget, "📋 日志")

    def init_wxauto(self):
        """初始化wxauto"""
        if not WXAUTO_AVAILABLE:
            self.log("❌ wxauto 未安装，请运行: pip install wxauto")
            self.status_bar.showMessage("wxauto 未安装")
            return

        try:
            self.wx_instance = WeChat()
            self.log(f"✅ wxauto 初始化成功，当前用户: {self.wx_instance.nickname}")
            self.status_bar.showMessage(f"已连接微信用户: {self.wx_instance.nickname}")
            self.refresh_sessions()
        except Exception as e:
            self.log(f"❌ wxauto 初始化失败: {e}")
            self.status_bar.showMessage("微信连接失败")
            QMessageBox.warning(self, "连接失败",
                              f"无法连接到微信:\n{e}\n\n请确保:\n1. 微信已登录\n2. 微信版本为3.9.X")

    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.log_text.append(log_message)
        print(log_message)  # 同时输出到控制台

    def refresh_sessions(self):
        """刷新会话列表"""
        if not self.wx_instance:
            return

        try:
            sessions = self.wx_instance.GetSession()

            # 更新下拉框
            self.target_combo.clear()
            self.listen_target_combo.clear()
            self.session_list.clear()

            for session in sessions:
                name = session.name
                self.target_combo.addItem(name)
                self.listen_target_combo.addItem(name)
                self.session_list.addItem(name)

            self.log(f"✅ 已刷新 {len(sessions)} 个会话")

        except Exception as e:
            self.log(f"❌ 刷新会话失败: {e}")

    def send_message(self):
        """发送消息"""
        if not self.wx_instance:
            QMessageBox.warning(self, "错误", "微信未连接")
            return

        target = self.target_combo.currentText().strip()
        message = self.message_text.toPlainText().strip()

        if not target:
            QMessageBox.warning(self, "错误", "请选择聊天对象")
            return

        if not message:
            QMessageBox.warning(self, "错误", "请输入消息内容")
            return

        try:
            # 处理@功能
            at_user = None
            if self.at_checkbox.isChecked():
                at_user = self.at_input.text().strip()

            # 发送消息
            result = self.wx_instance.SendMsg(message, who=target, at=at_user)

            if hasattr(result, 'success') and result.success:
                self.log(f"✅ 消息已发送到 {target}: {message[:30]}...")
                self.message_text.clear()
                self.status_bar.showMessage("消息发送成功", 3000)
            else:
                error_msg = getattr(result, 'message', '未知错误')
                self.log(f"❌ 消息发送失败: {error_msg}")
                QMessageBox.warning(self, "发送失败", f"消息发送失败:\n{error_msg}")

        except Exception as e:
            self.log(f"❌ 发送消息异常: {e}")
            QMessageBox.critical(self, "异常", f"发送消息时发生异常:\n{e}")

    def select_file(self):
        """选择文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择要发送的文件", "",
            "所有文件 (*);;图片 (*.jpg *.png *.gif);;视频 (*.mp4 *.avi);;文档 (*.pdf *.doc *.docx)"
        )
        if file_path:
            self.file_path_input.setText(file_path)

    def send_file(self):
        """发送文件"""
        if not self.wx_instance:
            QMessageBox.warning(self, "错误", "微信未连接")
            return

        target = self.target_combo.currentText().strip()
        file_path = self.file_path_input.text().strip()

        if not target:
            QMessageBox.warning(self, "错误", "请选择聊天对象")
            return

        if not file_path or not os.path.exists(file_path):
            QMessageBox.warning(self, "错误", "请选择有效的文件")
            return

        try:
            result = self.wx_instance.SendFiles(file_path, who=target)

            if hasattr(result, 'success') and result.success:
                file_name = os.path.basename(file_path)
                self.log(f"✅ 文件已发送到 {target}: {file_name}")
                self.file_record.append(f"[{datetime.now().strftime('%H:%M:%S')}] 发送到 {target}: {file_name}")
                self.file_path_input.clear()
                self.status_bar.showMessage("文件发送成功", 3000)
            else:
                error_msg = getattr(result, 'message', '未知错误')
                self.log(f"❌ 文件发送失败: {error_msg}")
                QMessageBox.warning(self, "发送失败", f"文件发送失败:\n{error_msg}")

        except Exception as e:
            self.log(f"❌ 发送文件异常: {e}")
            QMessageBox.critical(self, "异常", f"发送文件时发生异常:\n{e}")

    def get_messages(self):
        """获取消息"""
        if not self.wx_instance:
            return

        target = self.target_combo.currentText().strip()
        if not target:
            QMessageBox.warning(self, "错误", "请选择聊天对象")
            return

        try:
            # 切换到指定聊天
            self.wx_instance.ChatWith(target)

            # 获取消息
            messages = self.wx_instance.GetAllMessage()

            self.message_history.clear()
            self.message_history.append(f"=== {target} 的消息历史 ===\n")

            # 显示最近20条消息
            recent_messages = messages[-20:] if len(messages) > 20 else messages

            for msg in recent_messages:
                timestamp = datetime.now().strftime("%H:%M:%S")
                msg_text = f"[{timestamp}] [{msg.type}] {msg.sender}: {msg.content}\n"
                self.message_history.append(msg_text)

            self.log(f"✅ 已获取 {target} 的 {len(recent_messages)} 条消息")

        except Exception as e:
            self.log(f"❌ 获取消息失败: {e}")
            QMessageBox.warning(self, "获取失败", f"获取消息失败:\n{e}")

    def load_more_messages(self):
        """加载更多消息"""
        if not self.wx_instance:
            return

        try:
            result = self.wx_instance.LoadMoreMessage()
            if hasattr(result, 'success') and result.success:
                self.log("✅ 已加载更多历史消息")
                self.get_messages()  # 重新获取消息
            else:
                self.log("❌ 加载更多消息失败")

        except Exception as e:
            self.log(f"❌ 加载更多消息异常: {e}")

    def start_listening(self):
        """开始监听消息"""
        if not self.wx_instance:
            QMessageBox.warning(self, "错误", "微信未连接")
            return

        target = self.listen_target_combo.currentText().strip()
        if not target:
            QMessageBox.warning(self, "错误", "请选择监听对象")
            return

        if target in self.listener_threads:
            QMessageBox.information(self, "提示", f"已在监听 {target}")
            return

        try:
            # 创建监听线程
            listener = MessageListenerThread(self.wx_instance, target, self)
            listener.new_message.connect(self.on_new_message)

            self.listener_threads[target] = listener
            listener.start()

            self.log(f"✅ 开始监听 {target}")
            self.status_bar.showMessage(f"正在监听: {target}")

        except Exception as e:
            self.log(f"❌ 启动监听失败: {e}")
            QMessageBox.critical(self, "监听失败", f"启动监听失败:\n{e}")

    def stop_listening(self):
        """停止监听消息"""
        target = self.listen_target_combo.currentText().strip()
        if not target:
            QMessageBox.warning(self, "错误", "请选择要停止监听的对象")
            return

        if target not in self.listener_threads:
            QMessageBox.information(self, "提示", f"未在监听 {target}")
            return

        try:
            listener = self.listener_threads[target]
            listener.stop()
            del self.listener_threads[target]

            self.log(f"✅ 已停止监听 {target}")
            self.status_bar.showMessage("监听已停止")

        except Exception as e:
            self.log(f"❌ 停止监听失败: {e}")

    def on_new_message(self, sender, content, msg_type, chat_name):
        """处理新消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        message_text = f"[{timestamp}] [{msg_type}] {sender}: {content}\n"

        self.listen_messages.append(message_text)
        self.log(f"🔔 收到消息 - {chat_name}: {sender} - {content[:30]}...")

        # 自动回复
        if self.auto_reply_checkbox.isChecked():
            reply_text = self.reply_text.text().strip()
            if reply_text and "测试" in content:
                try:
                    self.wx_instance.SendMsg(reply_text, who=chat_name)
                    self.log(f"🤖 自动回复: {reply_text}")
                except Exception as e:
                    self.log(f"❌ 自动回复失败: {e}")

    def get_group_members(self):
        """获取群成员"""
        if not self.wx_instance:
            return

        target = self.target_combo.currentText().strip()
        if not target:
            QMessageBox.warning(self, "错误", "请选择群聊")
            return

        try:
            # 切换到指定聊天
            self.wx_instance.ChatWith(target)

            # 获取群成员
            members = self.wx_instance.GetGroupMembers()

            self.member_list.clear()
            if members:
                for member in members:
                    self.member_list.addItem(member)
                self.log(f"✅ 获取到 {len(members)} 个群成员")
            else:
                self.log("❌ 当前不是群聊或无法获取群成员")
                QMessageBox.information(self, "提示", "当前不是群聊或无法获取群成员")

        except Exception as e:
            self.log(f"❌ 获取群成员失败: {e}")
            QMessageBox.warning(self, "获取失败", f"获取群成员失败:\n{e}")

    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.log("📋 日志已清空")

    def save_log(self):
        """保存日志"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存日志", f"wxauto_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            "文本文件 (*.txt)"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.toPlainText())
                self.log(f"✅ 日志已保存到: {file_path}")
                QMessageBox.information(self, "保存成功", f"日志已保存到:\n{file_path}")
            except Exception as e:
                self.log(f"❌ 保存日志失败: {e}")
                QMessageBox.critical(self, "保存失败", f"保存日志失败:\n{e}")

    def closeEvent(self, event):
        """关闭事件"""
        # 停止所有监听线程
        for target, listener in self.listener_threads.items():
            try:
                listener.stop()
            except:
                pass

        # 停止wxauto监听
        if self.wx_instance:
            try:
                self.wx_instance.StopListening()
            except:
                pass

        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("wxauto GUI")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("wxauto")

    # 创建主窗口
    window = WxAutoGUI()
    window.show()

    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
