#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
wxauto 界面启动器
选择要使用的界面版本
"""

import sys
import os
from PySide6.QtWidgets import (
    QApplication, QDialog, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QGroupBox, QTextEdit
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont, QPixmap

class LauncherDialog(QDialog):
    """启动器对话框"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("wxauto 界面选择器")
        self.setFixedSize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("🚀 wxauto 可视化工具")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setStyleSheet("QLabel { color: #2196F3; margin: 10px; }")
        layout.addWidget(title_label)
        
        # 说明文字
        desc_label = QLabel("选择适合你的界面版本:")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setFont(QFont("Arial", 10))
        layout.addWidget(desc_label)
        
        # 版本选择
        version_group = QGroupBox("界面版本")
        version_layout = QVBoxLayout(version_group)
        
        # 简化版按钮
        simple_btn = QPushButton("🎯 简化版界面 (推荐新手)")
        simple_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 15px;
                border: none;
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        simple_btn.clicked.connect(self.launch_simple)
        
        # 完整版按钮
        full_btn = QPushButton("⚙️ 完整版界面 (功能丰富)")
        full_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 15px;
                border: none;
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        full_btn.clicked.connect(self.launch_full)
        
        version_layout.addWidget(simple_btn)
        version_layout.addWidget(full_btn)
        layout.addWidget(version_group)
        
        # 功能说明
        info_group = QGroupBox("功能说明")
        info_layout = QVBoxLayout(info_group)
        
        info_text = QTextEdit()
        info_text.setReadOnly(True)
        info_text.setMaximumHeight(120)
        info_text.setHtml("""
        <b>简化版界面:</b><br>
        • 界面简洁，操作简单<br>
        • 包含基本的发送消息、文件、监听功能<br>
        • 适合新手快速上手<br><br>
        
        <b>完整版界面:</b><br>
        • 功能完整，选项卡式布局<br>
        • 包含所有高级功能<br>
        • 适合需要完整功能的用户
        """)
        
        info_layout.addWidget(info_text)
        layout.addWidget(info_group)
        
        # 底部按钮
        bottom_layout = QHBoxLayout()
        
        help_btn = QPushButton("❓ 帮助")
        help_btn.clicked.connect(self.show_help)
        
        exit_btn = QPushButton("❌ 退出")
        exit_btn.clicked.connect(self.close)
        
        bottom_layout.addWidget(help_btn)
        bottom_layout.addStretch()
        bottom_layout.addWidget(exit_btn)
        
        layout.addLayout(bottom_layout)
        
    def launch_simple(self):
        """启动简化版界面"""
        self.close()
        try:
            # 关闭当前应用程序
            QApplication.instance().quit()

            # 启动新的应用程序
            import subprocess
            import sys
            subprocess.Popen([sys.executable, "wxauto_simple_gui.py"])

        except ImportError:
            self.show_error("找不到 wxauto_simple_gui.py 文件")
        except Exception as e:
            self.show_error(f"启动简化版界面失败: {e}")

    def launch_full(self):
        """启动完整版界面"""
        self.close()
        try:
            # 关闭当前应用程序
            QApplication.instance().quit()

            # 启动新的应用程序
            import subprocess
            import sys
            subprocess.Popen([sys.executable, "wxauto_gui.py"])

        except ImportError:
            self.show_error("找不到 wxauto_gui.py 文件")
        except Exception as e:
            self.show_error(f"启动完整版界面失败: {e}")
    
    def show_help(self):
        """显示帮助信息"""
        from PySide6.QtWidgets import QMessageBox
        
        help_text = """
wxauto 可视化工具使用说明:

1. 环境要求:
   • Windows 10/11 系统
   • Python 3.8+ 
   • 微信 3.9.X 版本已登录

2. 安装依赖:
   pip install wxauto PySide6

3. 功能介绍:
   • 发送文本消息和文件
   • 监听接收消息并自动回复
   • 获取聊天记录和群成员
   • 实时日志显示

4. 注意事项:
   • 仅用于学习和个人使用
   • 请遵守相关法律法规
   • 使用前确保微信已登录

如有问题，请查看项目文档或联系开发者。
        """
        
        QMessageBox.information(self, "使用帮助", help_text)
    
    def show_error(self, message):
        """显示错误信息"""
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.critical(self, "错误", message)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("wxauto 启动器")
    app.setApplicationVersion("1.0")
    
    # 创建启动器对话框
    launcher = LauncherDialog()
    launcher.show()
    
    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
