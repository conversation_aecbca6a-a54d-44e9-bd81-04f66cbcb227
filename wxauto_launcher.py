#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
wxauto 统一启动器
集成界面选择和主程序功能
"""

import sys
import os
import time
import threading
from datetime import datetime
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTextEdit, QLineEdit, QPushButton, QLabel, QComboBox, 
    QGroupBox, QCheckBox, QFileDialog, QMessageBox, QSplitter,
    QDialog, QStackedWidget, QTabWidget
)
from PySide6.QtCore import Qt, QThread, Signal
from PySide6.QtGui import QFont

try:
    from wxauto import WeChat
    WXAUTO_AVAILABLE = True
except ImportError:
    WXAUTO_AVAILABLE = False


class WelcomeWidget(QWidget):
    """欢迎页面"""
    
    switch_to_simple = Signal()
    switch_to_full = Signal()
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("🚀 wxauto 可视化工具")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 20, QFont.Bold))
        title_label.setStyleSheet("QLabel { color: #2196F3; margin: 20px; }")
        layout.addWidget(title_label)
        
        # 说明文字
        desc_label = QLabel("选择适合你的界面版本:")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setFont(QFont("Arial", 12))
        layout.addWidget(desc_label)
        
        # 版本选择按钮
        button_layout = QVBoxLayout()
        button_layout.setSpacing(20)
        
        # 简化版按钮
        simple_btn = QPushButton("🎯 简化版界面 (推荐新手)")
        simple_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 20px;
                border: none;
                border-radius: 10px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        simple_btn.clicked.connect(self.switch_to_simple.emit)
        
        # 完整版按钮
        full_btn = QPushButton("⚙️ 完整版界面 (功能丰富)")
        full_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 20px;
                border: none;
                border-radius: 10px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        full_btn.clicked.connect(self.switch_to_full.emit)
        
        button_layout.addWidget(simple_btn)
        button_layout.addWidget(full_btn)
        
        # 居中按钮
        center_layout = QHBoxLayout()
        center_layout.addStretch()
        center_layout.addLayout(button_layout)
        center_layout.addStretch()
        
        layout.addLayout(center_layout)
        
        # 功能说明
        info_text = QTextEdit()
        info_text.setReadOnly(True)
        info_text.setMaximumHeight(150)
        info_text.setHtml("""
        <div style="font-size: 12px;">
        <b>简化版界面:</b><br>
        • 界面简洁，操作简单<br>
        • 包含基本的发送消息、文件、监听功能<br>
        • 适合新手快速上手<br><br>
        
        <b>完整版界面:</b><br>
        • 功能完整，选项卡式布局<br>
        • 包含所有高级功能<br>
        • 适合需要完整功能的用户
        </div>
        """)
        
        layout.addWidget(info_text)
        layout.addStretch()


class SimpleGUIWidget(QWidget):
    """简化版界面"""
    
    back_to_welcome = Signal()
    
    def __init__(self):
        super().__init__()
        self.wx_instance = None
        self.init_ui()
        self.init_wxauto()
        
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 顶部工具栏
        toolbar_layout = QHBoxLayout()
        
        back_btn = QPushButton("← 返回")
        back_btn.clicked.connect(self.back_to_welcome.emit)
        back_btn.setStyleSheet("QPushButton { padding: 5px 10px; }")
        
        self.status_label = QLabel("准备就绪...")
        self.status_label.setStyleSheet("QLabel { color: blue; font-weight: bold; }")
        
        toolbar_layout.addWidget(back_btn)
        toolbar_layout.addWidget(self.status_label)
        toolbar_layout.addStretch()
        
        layout.addLayout(toolbar_layout)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)
        
        # 左侧：控制面板
        self.create_control_panel(splitter)
        
        # 右侧：日志显示
        self.create_log_panel(splitter)
        
        # 设置分割器比例
        splitter.setSizes([400, 400])
        
    def create_control_panel(self, parent):
        """创建控制面板"""
        control_widget = QWidget()
        layout = QVBoxLayout(control_widget)
        
        # 聊天对象选择
        chat_group = QGroupBox("💬 聊天对象")
        chat_layout = QVBoxLayout(chat_group)
        
        target_layout = QHBoxLayout()
        target_layout.addWidget(QLabel("选择聊天对象:"))
        self.target_combo = QComboBox()
        self.target_combo.setEditable(True)
        self.refresh_btn = QPushButton("🔄 刷新")
        self.refresh_btn.clicked.connect(self.refresh_sessions)
        
        target_layout.addWidget(self.target_combo)
        target_layout.addWidget(self.refresh_btn)
        chat_layout.addLayout(target_layout)
        
        layout.addWidget(chat_group)
        
        # 消息发送
        message_group = QGroupBox("📤 发送消息")
        message_layout = QVBoxLayout(message_group)
        
        self.message_input = QTextEdit()
        self.message_input.setMaximumHeight(80)
        self.message_input.setPlaceholderText("输入要发送的消息...")
        
        send_layout = QHBoxLayout()
        self.send_btn = QPushButton("📤 发送消息")
        self.send_btn.clicked.connect(self.send_message)
        self.send_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 6px; }")
        
        send_layout.addStretch()
        send_layout.addWidget(self.send_btn)
        
        message_layout.addWidget(self.message_input)
        message_layout.addLayout(send_layout)
        
        layout.addWidget(message_group)
        
        # 文件发送
        file_group = QGroupBox("📁 发送文件")
        file_layout = QVBoxLayout(file_group)
        
        file_select_layout = QHBoxLayout()
        self.file_input = QLineEdit()
        self.file_input.setPlaceholderText("选择要发送的文件...")
        self.select_file_btn = QPushButton("📂 选择文件")
        self.select_file_btn.clicked.connect(self.select_file)
        
        file_select_layout.addWidget(self.file_input)
        file_select_layout.addWidget(self.select_file_btn)
        
        send_file_layout = QHBoxLayout()
        self.send_file_btn = QPushButton("📁 发送文件")
        self.send_file_btn.clicked.connect(self.send_file)
        self.send_file_btn.setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; padding: 6px; }")
        
        send_file_layout.addStretch()
        send_file_layout.addWidget(self.send_file_btn)
        
        file_layout.addLayout(file_select_layout)
        file_layout.addLayout(send_file_layout)
        
        layout.addWidget(file_group)
        
        # 消息监听
        listen_group = QGroupBox("👂 消息监听")
        listen_layout = QVBoxLayout(listen_group)
        
        listen_control_layout = QHBoxLayout()
        self.start_listen_btn = QPushButton("▶️ 开始监听")
        self.stop_listen_btn = QPushButton("⏹️ 停止监听")
        
        self.start_listen_btn.clicked.connect(self.start_listening)
        self.stop_listen_btn.clicked.connect(self.stop_listening)
        self.start_listen_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; padding: 4px; }")
        self.stop_listen_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; padding: 4px; }")
        
        listen_control_layout.addWidget(self.start_listen_btn)
        listen_control_layout.addWidget(self.stop_listen_btn)
        
        # 自动回复
        auto_reply_layout = QHBoxLayout()
        self.auto_reply_check = QCheckBox("🤖 自动回复")
        self.reply_input = QLineEdit()
        self.reply_input.setPlaceholderText("自动回复内容...")
        self.reply_input.setEnabled(False)
        self.auto_reply_check.toggled.connect(self.reply_input.setEnabled)
        
        auto_reply_layout.addWidget(self.auto_reply_check)
        auto_reply_layout.addWidget(self.reply_input)
        
        listen_layout.addLayout(listen_control_layout)
        listen_layout.addLayout(auto_reply_layout)
        
        layout.addWidget(listen_group)
        
        # 其他功能
        other_group = QGroupBox("🔧 其他功能")
        other_layout = QVBoxLayout(other_group)
        
        other_btn_layout = QHBoxLayout()
        self.get_messages_btn = QPushButton("📨 获取消息")
        self.get_members_btn = QPushButton("👥 获取群成员")
        
        self.get_messages_btn.clicked.connect(self.get_messages)
        self.get_members_btn.clicked.connect(self.get_group_members)
        
        other_btn_layout.addWidget(self.get_messages_btn)
        other_btn_layout.addWidget(self.get_members_btn)
        
        other_layout.addLayout(other_btn_layout)
        layout.addWidget(other_group)
        
        layout.addStretch()
        parent.addWidget(control_widget)
        
    def create_log_panel(self, parent):
        """创建日志面板"""
        log_widget = QWidget()
        layout = QVBoxLayout(log_widget)
        
        # 日志标题
        log_label = QLabel("📋 操作日志")
        log_label.setStyleSheet("QLabel { font-weight: bold; font-size: 14px; }")
        layout.addWidget(log_label)
        
        # 日志显示
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.log_text)
        
        # 日志控制按钮
        log_btn_layout = QHBoxLayout()
        self.clear_log_btn = QPushButton("🗑️ 清空")
        self.save_log_btn = QPushButton("💾 保存")
        
        self.clear_log_btn.clicked.connect(self.clear_log)
        self.save_log_btn.clicked.connect(self.save_log)
        
        log_btn_layout.addWidget(self.clear_log_btn)
        log_btn_layout.addWidget(self.save_log_btn)
        log_btn_layout.addStretch()
        
        layout.addLayout(log_btn_layout)
        parent.addWidget(log_widget)

    def init_wxauto(self):
        """初始化wxauto"""
        if not WXAUTO_AVAILABLE:
            self.log("❌ wxauto 未安装，请运行: pip install wxauto")
            self.status_label.setText("❌ wxauto 未安装")
            self.status_label.setStyleSheet("QLabel { color: red; font-weight: bold; }")
            return

        try:
            self.wx_instance = WeChat()
            self.log(f"✅ 微信连接成功！当前用户: {self.wx_instance.nickname}")
            self.status_label.setText(f"✅ 已连接微信用户: {self.wx_instance.nickname}")
            self.status_label.setStyleSheet("QLabel { color: green; font-weight: bold; }")
            self.refresh_sessions()
        except Exception as e:
            self.log(f"❌ 微信连接失败: {e}")
            self.status_label.setText("❌ 微信连接失败")
            self.status_label.setStyleSheet("QLabel { color: red; font-weight: bold; }")
            QMessageBox.warning(self, "连接失败",
                              f"无法连接到微信:\n{e}\n\n请确保:\n1. 微信已登录\n2. 微信版本为3.9.X")

    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.log_text.append(log_message)
        print(log_message)

    def refresh_sessions(self):
        """刷新会话列表"""
        if not self.wx_instance:
            return

        try:
            sessions = self.wx_instance.GetSession()
            self.target_combo.clear()

            for session in sessions:
                self.target_combo.addItem(session.name)

            self.log(f"✅ 已刷新 {len(sessions)} 个会话")

        except Exception as e:
            self.log(f"❌ 刷新会话失败: {e}")

    def send_message(self):
        """发送消息"""
        if not self.wx_instance:
            QMessageBox.warning(self, "错误", "微信未连接")
            return

        target = self.target_combo.currentText().strip()
        message = self.message_input.toPlainText().strip()

        if not target or not message:
            QMessageBox.warning(self, "错误", "请选择聊天对象并输入消息内容")
            return

        try:
            result = self.wx_instance.SendMsg(message, who=target)
            self.log(f"✅ 消息已发送到 {target}: {message[:30]}...")
            self.message_input.clear()

        except Exception as e:
            self.log(f"❌ 发送消息失败: {e}")
            QMessageBox.critical(self, "发送失败", f"发送消息失败:\n{e}")

    def select_file(self):
        """选择文件"""
        file_path, _ = QFileDialog.getOpenFileName(self, "选择文件", "", "所有文件 (*)")
        if file_path:
            self.file_input.setText(file_path)

    def send_file(self):
        """发送文件"""
        if not self.wx_instance:
            QMessageBox.warning(self, "错误", "微信未连接")
            return

        target = self.target_combo.currentText().strip()
        file_path = self.file_input.text().strip()

        if not target or not file_path or not os.path.exists(file_path):
            QMessageBox.warning(self, "错误", "请选择聊天对象和有效文件")
            return

        try:
            result = self.wx_instance.SendFiles(file_path, who=target)
            file_name = os.path.basename(file_path)
            self.log(f"✅ 文件已发送到 {target}: {file_name}")
            self.file_input.clear()

        except Exception as e:
            self.log(f"❌ 发送文件失败: {e}")
            QMessageBox.critical(self, "发送失败", f"发送文件失败:\n{e}")

    def start_listening(self):
        """开始监听"""
        target = self.target_combo.currentText().strip()
        if not target:
            QMessageBox.warning(self, "错误", "请选择监听对象")
            return

        def message_handler(msg, chat):
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.log(f"🔔 [{timestamp}] {msg.sender}: {msg.content}")

            # 自动回复
            if self.auto_reply_check.isChecked():
                reply_text = self.reply_input.text().strip()
                if reply_text and "测试" in msg.content:
                    try:
                        self.wx_instance.SendMsg(reply_text, who=target)
                        self.log(f"🤖 自动回复: {reply_text}")
                    except:
                        pass

        try:
            self.wx_instance.AddListenChat(nickname=target, callback=message_handler)
            self.log(f"✅ 开始监听 {target}")

        except Exception as e:
            self.log(f"❌ 启动监听失败: {e}")

    def stop_listening(self):
        """停止监听"""
        target = self.target_combo.currentText().strip()
        if target:
            try:
                self.wx_instance.RemoveListenChat(nickname=target)
                self.log(f"✅ 已停止监听 {target}")
            except Exception as e:
                self.log(f"❌ 停止监听失败: {e}")

    def get_messages(self):
        """获取消息"""
        if not self.wx_instance:
            return

        target = self.target_combo.currentText().strip()
        if not target:
            QMessageBox.warning(self, "错误", "请选择聊天对象")
            return

        try:
            self.wx_instance.ChatWith(target)
            messages = self.wx_instance.GetAllMessage()

            self.log(f"📨 {target} 共有 {len(messages)} 条消息")

            # 显示最近5条消息
            for msg in messages[-5:]:
                self.log(f"  [{msg.type}] {msg.sender}: {msg.content[:50]}...")

        except Exception as e:
            self.log(f"❌ 获取消息失败: {e}")

    def get_group_members(self):
        """获取群成员"""
        if not self.wx_instance:
            return

        target = self.target_combo.currentText().strip()
        if not target:
            QMessageBox.warning(self, "错误", "请选择群聊")
            return

        try:
            self.wx_instance.ChatWith(target)
            members = self.wx_instance.GetGroupMembers()

            if members:
                self.log(f"👥 {target} 共有 {len(members)} 个成员:")
                for member in members[:10]:  # 只显示前10个
                    self.log(f"  - {member}")
                if len(members) > 10:
                    self.log(f"  ... 还有 {len(members) - 10} 个成员")
            else:
                self.log("❌ 当前不是群聊或无法获取群成员")

        except Exception as e:
            self.log(f"❌ 获取群成员失败: {e}")

    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.log("📋 日志已清空")

    def save_log(self):
        """保存日志"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存日志", f"wxauto_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            "文本文件 (*.txt)"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.toPlainText())
                self.log(f"✅ 日志已保存到: {file_path}")
            except Exception as e:
                self.log(f"❌ 保存日志失败: {e}")


class MainWindow(QMainWindow):
    """主窗口"""

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("wxauto 可视化工具")
        self.setGeometry(100, 100, 1000, 700)

        # 创建堆叠部件
        self.stacked_widget = QStackedWidget()
        self.setCentralWidget(self.stacked_widget)

        # 创建欢迎页面
        self.welcome_widget = WelcomeWidget()
        self.welcome_widget.switch_to_simple.connect(self.show_simple_gui)
        self.welcome_widget.switch_to_full.connect(self.show_full_gui)
        self.stacked_widget.addWidget(self.welcome_widget)

        # 创建简化版界面
        self.simple_widget = SimpleGUIWidget()
        self.simple_widget.back_to_welcome.connect(self.show_welcome)
        self.stacked_widget.addWidget(self.simple_widget)

        # 显示欢迎页面
        self.stacked_widget.setCurrentWidget(self.welcome_widget)

    def show_welcome(self):
        """显示欢迎页面"""
        self.stacked_widget.setCurrentWidget(self.welcome_widget)

    def show_simple_gui(self):
        """显示简化版界面"""
        self.stacked_widget.setCurrentWidget(self.simple_widget)

    def show_full_gui(self):
        """显示完整版界面"""
        QMessageBox.information(self, "提示", "完整版界面功能正在开发中，请使用简化版界面。")


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("wxauto 可视化工具")
    app.setApplicationVersion("1.0")

    # 创建主窗口
    window = MainWindow()
    window.show()

    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
