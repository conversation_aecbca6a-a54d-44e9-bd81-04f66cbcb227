from wxauto import WeChat
from wxauto.msgs import FriendMessage
import time
import os

# 初始化微信实例
wx = WeChat()

# ========== 基础功能演示 ==========
print("=== 基础功能演示 ===")

# 1. 发送文本消息
wx.SendMsg("这是测试消息", who="徐嘉昕测试群")

# 2. 发送文件（如果有的话）
# wx.SendFiles("C:/path/to/your/file.jpg", who="徐嘉昕测试群")

# 3. @某人发消息（如果是群聊）
# wx.SendMsg("大家好！", who="徐嘉昕测试群", at="某个群成员")

# 4. 获取当前聊天窗口的所有消息
msgs = wx.GetAllMessage()
print(f"共获取到 {len(msgs)} 条消息")
for msg in msgs[-5:]:  # 只显示最后5条
    print(f"[{msg.type}] {msg.sender}: {msg.content}")

# 5. 获取群成员（如果是群聊）
try:
    members = wx.GetGroupMembers()
    print(f"群成员: {members}")
except:
    print("当前不是群聊或获取群成员失败")

# 6. 获取会话列表
sessions = wx.GetSession()
print(f"会话列表: {[s.name for s in sessions[:5]]}")  # 显示前5个会话

# ========== 消息监听演示 ==========
print("\n=== 消息监听演示 ===")

def message_handler(msg, chat):
    """消息处理函数"""
    print(f"\n收到新消息:")
    print(f"  发送者: {msg.sender}")
    print(f"  内容: {msg.content}")
    print(f"  类型: {msg.type}")

    # 保存消息到文件
    with open('received_messages.txt', 'a', encoding='utf-8') as f:
        f.write(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {msg.sender}: {msg.content}\n")

    # 自动回复示例
    if "测试" in msg.content:
        msg.reply("我收到了你的测试消息！")

    # 下载文件示例
    if msg.type in ['image', 'video', 'file']:
        try:
            file_path = msg.download()
            print(f"  文件已下载: {file_path}")
        except:
            print("  文件下载失败")

    # 语音转文字示例
    if msg.type == 'voice':
        try:
            text = msg.to_text()
            print(f"  语音内容: {text}")
        except:
            print("  语音转文字失败")

# 添加监听（注意：这会打开一个新的聊天窗口）
print("添加消息监听...")
# wx.AddListenChat(nickname="徐嘉昕测试群", callback=message_handler)

print("\n程序运行中，按 Ctrl+C 停止...")
print("你可以:")
print("1. 在微信中发送消息测试自动回复")
print("2. 发送图片/视频测试自动下载")
print("3. 发送语音测试语音转文字")

# 保持程序运行（用于监听消息）
# wx.KeepRunning()  # 取消注释以启用监听