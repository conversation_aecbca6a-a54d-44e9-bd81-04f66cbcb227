#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
wxauto 完整功能演示
包含所有主要功能的使用示例
"""

from wxauto import WeChat
from wxauto.msgs import FriendMessage
import time
import os

def main():
    print("🚀 wxauto 功能演示程序")
    print("=" * 50)
    
    # 初始化微信实例
    try:
        wx = WeChat()
        print(f"✅ 微信实例初始化成功，当前用户: {wx.nickname}")
    except Exception as e:
        print(f"❌ 微信初始化失败: {e}")
        return
    
    # 获取会话列表
    print("\n📋 当前会话列表:")
    try:
        sessions = wx.GetSession()
        for i, session in enumerate(sessions[:10], 1):
            print(f"  {i}. {session.name}")
    except Exception as e:
        print(f"❌ 获取会话列表失败: {e}")
    
    # 选择聊天对象
    chat_target = input("\n请输入要测试的聊天对象名称: ").strip()
    if not chat_target:
        print("❌ 未输入聊天对象，程序退出")
        return
    
    # 切换到指定聊天
    try:
        wx.ChatWith(chat_target)
        print(f"✅ 已切换到聊天: {chat_target}")
    except Exception as e:
        print(f"❌ 切换聊天失败: {e}")
        return
    
    # 功能菜单
    while True:
        print("\n" + "=" * 50)
        print("🎯 请选择要测试的功能:")
        print("1. 发送文本消息")
        print("2. 发送文件")
        print("3. 获取聊天消息")
        print("4. 获取群成员 (仅群聊)")
        print("5. 启动消息监听")
        print("6. @某人发消息 (仅群聊)")
        print("7. 获取聊天信息")
        print("8. 加载更多历史消息")
        print("0. 退出程序")
        
        choice = input("\n请输入选项 (0-8): ").strip()
        
        if choice == "1":
            test_send_message(wx, chat_target)
        elif choice == "2":
            test_send_file(wx, chat_target)
        elif choice == "3":
            test_get_messages(wx)
        elif choice == "4":
            test_get_group_members(wx)
        elif choice == "5":
            test_message_listening(wx, chat_target)
        elif choice == "6":
            test_at_message(wx, chat_target)
        elif choice == "7":
            test_chat_info(wx)
        elif choice == "8":
            test_load_more_messages(wx)
        elif choice == "0":
            print("👋 程序退出")
            break
        else:
            print("❌ 无效选项，请重新输入")

def test_send_message(wx, chat_target):
    """测试发送消息"""
    print("\n📤 发送消息测试")
    message = input("请输入要发送的消息: ").strip()
    if message:
        try:
            result = wx.SendMsg(message, who=chat_target)
            if result.success:
                print("✅ 消息发送成功")
            else:
                print(f"❌ 消息发送失败: {result.message}")
        except Exception as e:
            print(f"❌ 发送消息异常: {e}")
    else:
        print("❌ 消息内容不能为空")

def test_send_file(wx, chat_target):
    """测试发送文件"""
    print("\n📁 发送文件测试")
    file_path = input("请输入文件路径: ").strip()
    if file_path and os.path.exists(file_path):
        try:
            result = wx.SendFiles(file_path, who=chat_target)
            if result.success:
                print("✅ 文件发送成功")
            else:
                print(f"❌ 文件发送失败: {result.message}")
        except Exception as e:
            print(f"❌ 发送文件异常: {e}")
    else:
        print("❌ 文件路径无效或文件不存在")

def test_get_messages(wx):
    """测试获取消息"""
    print("\n📨 获取消息测试")
    try:
        # 获取所有消息
        all_msgs = wx.GetAllMessage()
        print(f"📋 共获取到 {len(all_msgs)} 条消息")
        
        # 显示最近5条消息
        recent_msgs = all_msgs[-5:] if len(all_msgs) >= 5 else all_msgs
        print("\n最近的消息:")
        for i, msg in enumerate(recent_msgs, 1):
            print(f"  {i}. [{msg.type}] {msg.sender}: {msg.content[:50]}...")
        
        # 获取新消息
        new_msgs = wx.GetNewMessage()
        print(f"\n🆕 新消息数量: {len(new_msgs)}")
        
    except Exception as e:
        print(f"❌ 获取消息失败: {e}")

def test_get_group_members(wx):
    """测试获取群成员"""
    print("\n👥 获取群成员测试")
    try:
        members = wx.GetGroupMembers()
        if members:
            print(f"📋 群成员列表 (共{len(members)}人):")
            for i, member in enumerate(members, 1):
                print(f"  {i}. {member}")
        else:
            print("❌ 当前不是群聊或无法获取群成员")
    except Exception as e:
        print(f"❌ 获取群成员失败: {e}")

def test_at_message(wx, chat_target):
    """测试@消息"""
    print("\n📢 @消息测试")
    at_user = input("请输入要@的用户名: ").strip()
    message = input("请输入消息内容: ").strip()
    
    if at_user and message:
        try:
            result = wx.SendMsg(message, who=chat_target, at=at_user)
            if result.success:
                print("✅ @消息发送成功")
            else:
                print(f"❌ @消息发送失败: {result.message}")
        except Exception as e:
            print(f"❌ 发送@消息异常: {e}")
    else:
        print("❌ 用户名和消息内容不能为空")

def test_chat_info(wx):
    """测试获取聊天信息"""
    print("\n📊 聊天信息测试")
    try:
        chat_info = wx.ChatInfo()
        print("聊天信息:")
        for key, value in chat_info.items():
            print(f"  {key}: {value}")
    except Exception as e:
        print(f"❌ 获取聊天信息失败: {e}")

def test_load_more_messages(wx):
    """测试加载更多消息"""
    print("\n⬆️ 加载更多消息测试")
    try:
        result = wx.LoadMoreMessage()
        if result.success:
            print("✅ 成功加载更多历史消息")
        else:
            print(f"❌ 加载失败: {result.message}")
    except Exception as e:
        print(f"❌ 加载更多消息异常: {e}")

def test_message_listening(wx, chat_target):
    """测试消息监听"""
    print("\n👂 消息监听测试")
    print("⚠️  注意：这将打开一个新的聊天窗口用于监听")
    
    confirm = input("是否继续? (y/n): ").strip().lower()
    if confirm != 'y':
        return
    
    def message_handler(msg, chat):
        """消息处理函数"""
        print(f"\n🔔 收到新消息:")
        print(f"  发送者: {msg.sender}")
        print(f"  内容: {msg.content}")
        print(f"  类型: {msg.type}")
        
        # 保存到文件
        with open('监听消息记录.txt', 'a', encoding='utf-8') as f:
            timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
            f.write(f"[{timestamp}] {msg.sender}: {msg.content}\n")
        
        # 简单自动回复
        if "测试" in msg.content:
            try:
                msg.reply("🤖 自动回复：收到测试消息")
            except:
                pass
        
        # 文件下载
        if msg.type in ['image', 'video', 'file']:
            try:
                file_path = msg.download()
                print(f"  📁 文件已下载: {file_path}")
            except Exception as e:
                print(f"  ❌ 文件下载失败: {e}")
    
    try:
        # 添加监听
        result = wx.AddListenChat(nickname=chat_target, callback=message_handler)
        if hasattr(result, 'success') and not result.success:
            print(f"❌ 添加监听失败: {result.message}")
            return
        
        print("✅ 消息监听已启动")
        print("💡 现在可以在微信中发送消息测试自动回复")
        print("⏹️  按 Ctrl+C 停止监听")
        
        # 保持监听
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n⏹️  停止监听...")
            wx.RemoveListenChat(nickname=chat_target)
            print("✅ 监听已停止")
            
    except Exception as e:
        print(f"❌ 消息监听异常: {e}")

if __name__ == "__main__":
    main()
