# wxauto 可视化界面使用说明

## 🎯 概述

为了让 wxauto 更容易使用，我们创建了基于 PySide6 的可视化界面，让你可以通过图形界面操作微信，无需编写代码。

## 📋 文件说明

| 文件名 | 说明 | 适用人群 |
|--------|------|----------|
| `启动界面.py` | 界面选择器，选择要使用的版本 | 所有用户 |
| `wxauto_simple_gui.py` | 简化版界面，功能精简 | 新手用户 |
| `wxauto_gui.py` | 完整版界面，功能丰富 | 高级用户 |

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install PySide6
```

### 2. 启动界面

```bash
python 启动界面.py
```

### 3. 选择界面版本

- **简化版**: 适合新手，界面简洁，操作简单
- **完整版**: 功能完整，适合需要高级功能的用户

## 🎯 简化版界面功能

### 主要功能
- ✅ 发送文本消息
- ✅ 发送文件（图片、视频、文档等）
- ✅ 消息监听和自动回复
- ✅ 获取聊天记录
- ✅ 获取群成员列表
- ✅ 实时操作日志

### 界面布局
```
┌─────────────────┬─────────────────┐
│   控制面板      │    操作日志     │
│                 │                 │
│ • 聊天对象选择  │ • 实时日志显示  │
│ • 消息发送      │ • 操作结果      │
│ • 文件发送      │ • 错误信息      │
│ • 消息监听      │                 │
│ • 其他功能      │                 │
└─────────────────┴─────────────────┘
```

## ⚙️ 完整版界面功能

### 选项卡功能
1. **💬 消息发送**
   - 发送文本消息
   - @某人功能
   - 消息历史查看
   - 加载更多历史消息

2. **📁 文件发送**
   - 选择和发送文件
   - 发送记录查看
   - 支持多种文件格式

3. **👂 消息监听**
   - 实时监听消息
   - 自动回复设置
   - 监听消息显示

4. **👥 联系人**
   - 会话列表管理
   - 群成员查看
   - 联系人信息

5. **📋 日志**
   - 详细操作日志
   - 日志保存功能
   - 日志清空功能

## 📖 使用教程

### 基本使用流程

1. **启动程序**
   ```bash
   python 启动界面.py
   ```

2. **选择界面版本**
   - 新手推荐选择"简化版界面"
   - 需要完整功能选择"完整版界面"

3. **连接微信**
   - 确保微信已登录
   - 程序会自动连接微信
   - 连接成功后显示当前用户名

4. **刷新会话列表**
   - 点击"🔄 刷新"按钮
   - 获取当前所有聊天会话

5. **发送消息**
   - 选择聊天对象
   - 输入消息内容
   - 点击"📤 发送消息"

6. **发送文件**
   - 点击"📂 选择文件"
   - 选择要发送的文件
   - 点击"📁 发送文件"

7. **启动监听**
   - 选择要监听的聊天对象
   - 可选择启用自动回复
   - 点击"▶️ 开始监听"

### 高级功能

#### 消息监听和自动回复
```python
# 监听设置
1. 选择监听对象
2. 勾选"🤖 自动回复"
3. 输入自动回复内容
4. 点击"▶️ 开始监听"

# 自动回复触发条件
- 当收到包含"测试"的消息时
- 自动回复设置的内容
```

#### 群聊功能
```python
# 获取群成员
1. 选择群聊
2. 点击"👥 获取群成员"
3. 查看群成员列表

# @某人发消息（完整版）
1. 勾选"@某人"
2. 输入要@的用户名
3. 发送消息
```

## ⚠️ 注意事项

### 系统要求
- **操作系统**: Windows 10/11 或 Windows Server 2016+
- **Python版本**: 3.8 - 3.15
- **微信版本**: 3.9.X 系列

### 使用限制
- 仅支持 Windows 系统
- 微信必须已登录
- 仅用于学习和个人使用
- 请遵守相关法律法规

### 常见问题

#### Q: 程序启动失败？
A: 检查以下几点：
1. 确保已安装 PySide6: `pip install PySide6`
2. 确保微信已登录
3. 确保微信版本为 3.9.X

#### Q: 无法发送消息？
A: 检查以下几点：
1. 确保选择了正确的聊天对象
2. 确保消息内容不为空
3. 确保微信窗口没有被其他程序遮挡

#### Q: 监听功能不工作？
A: 检查以下几点：
1. 确保选择了正确的监听对象
2. 确保微信聊天窗口是打开的
3. 尝试重新启动监听

#### Q: 界面显示异常？
A: 尝试以下解决方案：
1. 重新启动程序
2. 检查屏幕分辨率和缩放设置
3. 更新 PySide6 到最新版本

## 🔧 自定义和扩展

### 修改自动回复逻辑
在 `wxauto_simple_gui.py` 或 `wxauto_gui.py` 中找到消息处理函数，可以自定义回复逻辑：

```python
def message_handler(msg, chat):
    # 自定义回复逻辑
    if "关键词" in msg.content:
        # 执行特定操作
        pass
```

### 添加新功能
可以基于现有代码添加新的功能按钮和处理逻辑。

## 📞 技术支持

如果遇到问题：
1. 查看程序日志中的错误信息
2. 确认环境配置是否正确
3. 参考 wxauto 官方文档
4. 在项目 GitHub 页面提交 Issue

## 📄 许可证

本项目仅用于学习和研究目的，请勿用于商业用途。使用时请遵守相关法律法规。
