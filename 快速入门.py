#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
wxauto 快速入门示例
适合新手的简单示例
"""

from wxauto import WeChat
import time

def main():
    print("🚀 wxauto 快速入门")
    print("=" * 30)
    
    # 1. 初始化微信
    print("正在连接微信...")
    try:
        wx = WeChat()
        print(f"✅ 连接成功！当前用户: {wx.nickname}")
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        print("请确保:")
        print("  1. 微信已经登录")
        print("  2. 微信版本是 3.9.X")
        print("  3. 已安装 wxauto: pip install wxauto")
        return
    
    # 2. 发送简单消息
    print("\n📤 发送消息测试")
    target = input("请输入要发送消息的联系人或群名: ").strip()
    if target:
        message = "Hello! 这是来自 wxauto 的测试消息 🤖"
        try:
            result = wx.SendMsg(message, who=target)
            print("✅ 消息发送成功!")
        except Exception as e:
            print(f"❌ 发送失败: {e}")
    
    # 3. 获取消息
    print("\n📨 获取最近消息")
    try:
        msgs = wx.GetAllMessage()
        print(f"共获取到 {len(msgs)} 条消息")
        
        # 显示最后3条消息
        for msg in msgs[-3:]:
            print(f"  {msg.sender}: {msg.content[:30]}...")
    except Exception as e:
        print(f"❌ 获取消息失败: {e}")
    
    # 4. 简单的消息监听示例
    print("\n👂 消息监听演示")
    listen_choice = input("是否启动消息监听? (y/n): ").strip().lower()
    
    if listen_choice == 'y':
        def simple_handler(msg, chat):
            print(f"🔔 新消息: {msg.sender} 说: {msg.content}")
            
            # 简单自动回复
            if "你好" in msg.content:
                try:
                    msg.reply("你好！我是机器人 🤖")
                except:
                    pass
        
        try:
            wx.AddListenChat(nickname=target, callback=simple_handler)
            print("✅ 监听已启动，发送消息试试吧!")
            print("💡 发送包含'你好'的消息会自动回复")
            print("⏹️  按 Ctrl+C 停止")
            
            # 保持运行
            while True:
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n⏹️  停止监听")
            wx.RemoveListenChat(nickname=target)
        except Exception as e:
            print(f"❌ 监听失败: {e}")
    
    print("\n👋 程序结束")

if __name__ == "__main__":
    main()
