[project]
name = "wxauto"
version = "39.1.15"
description = "wxauto 3.9 V2 version"
authors = [
    {name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.8,<=3.15"
dependencies = [
    "tenacity",
    "pywin32",
    "pyperclip",
    "pillow",
    "psutil",
    "colorama",
    "comtypes"
]

[tool.setuptools.packages.find]
include = ["wxauto*"]

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"